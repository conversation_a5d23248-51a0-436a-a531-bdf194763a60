package vn.osp.common.domain.domainentitytypes;

import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.Instant;

/**
 * Model có created by (người tạo) và created (thời gian tạo)
 *
 * @param <TKey> ki<PERSON>u dữ liệu kh<PERSON> (thường là UUI<PERSON>, Long, Integer,...)
 */
public interface HasCreatedBy<TKey extends Serializable> {

    /**
     * Thời gian tạo
     */
    Instant getCreated();

    void setCreated(Instant created);

    /**
     * Id người tạo
     */
    @Nullable
    TKey getCreatedBy();

    void setCreatedBy(@Nullable TKey createdBy);
}
