package vn.osp.common.domain.helpers;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.RandomStringGenerator;
import org.jsoup.Jsoup;
import org.jsoup.safety.Safelist;

import java.text.Normalizer;
import java.util.regex.Pattern;

public final class StringHelper {
    public static final String EMPTY = "";

    private StringHelper() {
    }

    /**
     * Kiểm tra chuỗi có rỗng hoặc null không
     *
     * @param value Chuỗi cần kiểm tra
     * @return true nếu chuỗi null, rỗng hoặc chỉ chứa khoảng trắng; false nếu ngược lại
     */
    public static boolean isEmpty(String value) {
        return value == null || StringUtils.isBlank(value);
    }

    /**
     * Trả về giá trị mặc định nếu chuỗi rỗng hoặc null
     *
     * @param value      Chuỗi cần kiểm tra
     * @param defaultVal Giá trị mặc định
     * @return Giá trị ban đầu nếu không rỗng, ngược lại trả về giá trị mặc định
     */
    public static String defaultIfEmpty(String value, String defaultVal) {
        return isEmpty(value) ? defaultVal : value;
    }

    /**
     * Loại bỏ khoảng trắng ở đầu và cuối chuỗi một cách an toàn
     *
     * @param value Chuỗi cần trim
     * @return Chuỗi đã được trim hoặc null nếu đầu vào là null
     */
    public static String safeTrim(String value) {
        return value != null ? value.trim() : null;
    }

    /**
     * Tạo chuỗi ngẫu nhiên chỉ chứa các ký tự số
     *
     * @param length Độ dài của chuỗi cần tạo
     * @return Chuỗi ngẫu nhiên chỉ chứa các ký tự từ 0-9
     * @throws IllegalArgumentException nếu length < 0
     */
    public static String randomNumeric(int length) {
        if (length <= 0) {
            throw new IllegalArgumentException("Count must be > 0");
        }
        RandomStringGenerator generator = new RandomStringGenerator.Builder().withinRange('0', '9').get();

        return generator.generate(length);
    }

    /**
     * Tạo chuỗi ngẫu nhiên từ tập ký tự chỉ định
     *
     * @param length         Độ dài của chuỗi cần tạo
     * @param acceptCharters Tập ký tự được phép sử dụng để tạo chuỗi ngẫu nhiên
     * @return Chuỗi ngẫu nhiên với độ dài xác định từ tập ký tự chỉ định
     * @throws IllegalArgumentException nếu length < 0 hoặc acceptCharters null/rỗng
     */
    public static String random(int length, String acceptCharters) {
        if (length < 0) {
            throw new IllegalArgumentException("Length must be >= 0");
        }
        if (acceptCharters == null || acceptCharters.isEmpty()) {
            throw new IllegalArgumentException("acceptCharters must not be null or empty");
        }

        // Tạo generator từ tập ký tự cho phép
        RandomStringGenerator generator = new RandomStringGenerator.Builder().selectFrom(acceptCharters.toCharArray()).get();

        return generator.generate(length);
    }

    /**
     * Chuyển chuỗi tiếng Việt có dấu thành tiếng Việt không dấu
     *
     * @param input Chuỗi tiếng Việt có dấu cần xử lý
     * @return Chuỗi tiếng Việt không dấu, trả về chuỗi rỗng nếu input null hoặc rỗng
     */
    public static String normalizeVietnamese(String input) {
        if (StringHelper.isEmpty(input)) {
            return StringHelper.EMPTY;
        }
        // Chuẩn hóa chuỗi Unicode về dạng tổ hợp
        String normalized = Normalizer.normalize(input, Normalizer.Form.NFD);
        // Regex loại bỏ các ký tự dấu (diacritical marks)
        Pattern pattern = Pattern.compile("\\p{M}+");
        String result = pattern.matcher(normalized).replaceAll("");

        // Xử lý riêng chữ đ/Đ
        result = result.replace("đ", "d").replace("Đ", "D");

        return result;
    }

    /**
     * Chuyển chuỗi thành camelCase
     * <p>
     * Method này chỉ thực hiện viết thường ký tự đầu tiên của chuỗi,
     * không xử lý việc tách từ hoặc xử lý các ký tự đặc biệt
     *
     * @param input Chuỗi cần chuyển
     * @return Chuỗi đã chuyển thành camelCase, trả về input nếu input null hoặc rỗng
     */
    public static String toCamelCase(String input) {
        if (StringHelper.isEmpty(input)) {
            return input;
        }

        StringBuilder sb = new StringBuilder(input.length());
        sb.append(Character.toLowerCase(input.charAt(0)));
        if (input.length() > 1) {
            sb.append(input.substring(1));
        }
        return sb.toString();
    }

    /**
     * Chuyển chuỗi thành snake_case
     *
     * @param input Chuỗi cần chuyển (hỗ trợ camelCase, PascalCase, kebab-case, space-separated)
     * @return Chuỗi đã chuyển thành snake_case
     */
    public static String toSnakeCase(String input) {
        if (StringHelper.isEmpty(input)) {
            return StringHelper.EMPTY;
        }

        String result = input.trim();

        // Thay thế dấu gạch ngang và khoảng trắng bằng underscore
        result = result.replaceAll("[-\\s]+", "_");

        // Chèn underscore trước các chữ cái viết hoa (camelCase -> snake_case)
        result = result.replaceAll("([a-z])([A-Z])", "$1_$2");

        // Chuyển tất cả về lowercase
        return result.toLowerCase();
    }

    /**
     * Viết hoa chữ cái đầu tiên của chuỗi
     *
     * @param input Chuỗi cần viết hoa
     * @return Chuỗi đã viết hoa chữ cái đầu tiên, trả về null nếu input null
     */
    public static String capitalize(String input) {
        return StringUtils.capitalize(input);
    }

    /**
     * Loại bỏ toàn bộ HTML, chỉ giữ lại plain text.
     *
     * @param input chuỗi HTML
     * @return plain text
     */
    public static String stripHtml(String input) {
        if (StringHelper.isEmpty(input)) return StringHelper.EMPTY;
        return Jsoup.parse(input).text();
    }

    /**
     * Làm sạch HTML, chỉ cho phép một số thẻ cơ bản (b, i, strong, em, a, ul, ol, li...).
     *
     * @param input chuỗi HTML
     * @return HTML an toàn
     */
    public static String sanitizeHtml(String input) {
        if (StringHelper.isEmpty(input)) return StringHelper.EMPTY;
        return Jsoup.clean(input, Safelist.basic());
    }
}