package vn.osp.common.domain.helpers;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import java.io.File;
import java.io.IOException;

public final class JsonHelper {
    /**
     * ObjectMapper instance được cấu hình sẵn với các settings tối ưu cho JSON processing
     * <p>
     * <PERSON><PERSON><PERSON> hình bao gồm:
     * - Bỏ qua các trường null khi serialize
     * - Sử dụng camelCase naming strategy
     * - Bỏ qua reference cycles
     * - Format JSON với indentation
     * - Hỗ trợ Java 8 time types
     * - Không fail khi gặp unknown properties
     * </p>
     */
    private static final ObjectMapper mapper = JsonMapper.builder()
            .serializationInclusion(JsonInclude.Include.NON_NULL) // ignore null
            .propertyNamingStrategy(PropertyNamingStrategies.LOWER_CAMEL_CASE) // camelCase
            .disable(SerializationFeature.FAIL_ON_SELF_REFERENCES) // ignore reference cycles
            .enable(SerializationFeature.INDENT_OUTPUT) // đẹp hơn (tùy chọn)
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS) //
            .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES) // tránh lỗi khi json có trường lạ
            .addModule(new JavaTimeModule())
            .build();

    /**
     * Private constructor để ngăn khởi tạo instance của utility class
     */
    private JsonHelper() {
    }

    /**
     * Chuyển đổi một object thành chuỗi JSON
     *
     * @param obj Đối tượng cần chuyển đổi thành JSON
     * @return Chuỗi JSON tương ứng, hoặc chuỗi rỗng nếu obj là null
     * @throws JsonProcessingException nếu có lỗi trong quá trình serialize
     */
    public static String toJson(Object obj) throws JsonProcessingException {
        if (obj == null) {
            return StringHelper.EMPTY;
        }
        return mapper.writeValueAsString(obj);
    }

    /**
     * Chuyển đổi chuỗi JSON thành object kiểu T
     *
     * @param json  Chuỗi JSON cần deserialize
     * @param clazz Class của đối tượng đích
     * @param <T>   Kiểu dữ liệu của đối tượng
     * @return Đối tượng kiểu T, hoặc null nếu json là null hoặc rỗng
     * @throws JsonProcessingException nếu có lỗi trong quá trình deserialize
     */
    public static <T> T fromJson(String json, Class<T> clazz) throws JsonProcessingException {
        if (StringHelper.isEmpty(json)) {
            return null;
        }

        return mapper.readValue(json, clazz);
    }

    /**
     * Chuyển đổi chuỗi JSON thành object kiểu T với TypeReference
     *
     * @param json    Chuỗi JSON cần deserialize
     * @param typeRef TypeReference chứa thông tin kiểu dữ liệu phức tạp (như generic types)
     * @param <T>     Kiểu dữ liệu của đối tượng
     * @return Đối tượng kiểu T, hoặc null nếu json là null hoặc rỗng
     * @throws JsonProcessingException nếu có lỗi trong quá trình deserialize
     */
    public static <T> T fromJson(String json, TypeReference<T> typeRef) throws JsonProcessingException {
        if (StringHelper.isEmpty(json)) {
            return null;
        }

        return mapper.readValue(json, typeRef);
    }

    /**
     * Chuyển đổi mảng byte thành object kiểu T
     *
     * @param bytes Mảng byte cần chuyển đổi
     * @param type  Class của đối tượng cần deserialize
     * @param <T>   Kiểu dữ liệu của đối tượng
     * @return Đối tượng kiểu T, hoặc null nếu bytes là null hoặc rỗng
     * @throws IOException nếu có lỗi trong quá trình đọc dữ liệu
     */
    public static <T> T deserialize(byte[] bytes, Class<T> type) throws IOException {
        if (bytes == null || bytes.length == 0) {
            return null;
        }

        return mapper.readValue(bytes, type);
    }

    /**
     * Chuyển đổi mảng byte thành object kiểu T với TypeReference
     *
     * @param bytes   Mảng byte cần chuyển đổi
     * @param typeRef TypeReference chứa thông tin kiểu dữ liệu của đối tượng cần deserialize
     * @param <T>     Kiểu dữ liệu của đối tượng
     * @return Đối tượng kiểu T, hoặc null nếu bytes là null hoặc rỗng
     * @throws IOException nếu có lỗi trong quá trình đọc dữ liệu
     */
    public static <T> T deserialize(byte[] bytes, TypeReference<T> typeRef) throws IOException {
        if (bytes == null || bytes.length == 0) {
            return null;
        }

        return mapper.readValue(bytes, typeRef);
    }

    /**
     * Chuyển đổi object thành mảng byte
     *
     * @param obj Đối tượng cần serialize
     * @return Mảng byte chứa dữ liệu JSON, hoặc null nếu obj là null
     * @throws JsonProcessingException nếu có lỗi trong quá trình serialize
     */
    public static byte[] serialize(Object obj) throws JsonProcessingException {
        if (obj == null) {
            return new byte[0];
        }
        return mapper.writeValueAsBytes(obj);
    }

    /**
     * Chuyển đổi object thành file JSON
     *
     * @param obj      Đối tượng cần serialize
     * @param filePath Đường dẫn file đích
     * @throws IOException nếu có lỗi trong quá trình ghi file
     */
    public static void serialize(Object obj, String filePath) throws IOException {
        if (obj == null) return;

        mapper.writeValue(new File(filePath), obj);
    }

    /**
     * Chuyển đổi file JSON thành object kiểu T
     *
     * @param filePath Đường dẫn file JSON
     * @param clazz    Class của đối tượng đích
     * @param <T>      Kiểu dữ liệu của đối tượng
     * @return Đối tượng kiểu T, hoặc null nếu file không tồn tại
     * @throws IOException nếu có lỗi trong quá trình đọc file
     */
    public static <T> T deserialize(String filePath, Class<T> clazz) throws IOException {
        File file = new File(filePath);
        if (!file.exists()) return null;

        return mapper.readValue(file, clazz);
    }

    /**
     * Chuyển đổi file JSON thành object kiểu T với TypeReference
     *
     * @param filePath Đường dẫn file JSON
     * @param typeRef  TypeReference chứa thông tin kiểu dữ liệu của đối tượng đích
     * @param <T>      Kiểu dữ liệu của đối tượng
     * @return Đối tượng kiểu T, hoặc null nếu file không tồn tại
     * @throws IOException nếu có lỗi trong quá trình đọc file
     */
    public static <T> T deserialize(String filePath, TypeReference<T> typeRef) throws IOException {
        File file = new File(filePath);
        if (!file.exists()) return null;

        return mapper.readValue(file, typeRef);
    }
}