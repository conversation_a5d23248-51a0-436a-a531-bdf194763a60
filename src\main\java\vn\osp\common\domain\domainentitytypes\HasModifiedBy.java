package vn.osp.common.domain.domainentitytypes;

import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.Instant;

/**
 * Model có modified by (người sửa) và modified (thời gian sửa).
 *
 * @param <TKey> Ki<PERSON>u dữ liệu của Id (UUID, Long, Integer, ...)
 */
public interface HasModifiedBy<TKey extends Serializable> {

    /**
     * Thời gian sửa.
     */
    @Nullable
    Instant getModified();

    void setModified(@Nullable Instant modified);

    /**
     * Id người sửa.
     */
    @Nullable
    TKey getModifiedBy();

    void setModifiedBy(@Nullable TKey modifiedBy);
}
