package vn.osp.common.domain.helpers;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.util.Base64;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("EncryptHelper Tests")
class EncryptHelperTests {

    private static final String SAMPLE_TEXT = "Hello World";
    private static final String EMPTY_STRING = "";
    private static final String SAMPLE_PASSWORD = "mySecretPassword123";
    
    @Nested
    @DisplayName("Hashing Tests")
    class HashingTests {

        @Test
        @DisplayName("SHA-256 hash cho byte array")
        void testSha256Bytes() throws NoSuchAlgorithmException {
            byte[] data = SAMPLE_TEXT.getBytes(StandardCharsets.UTF_8);
            byte[] hash = EncryptHelper.sha256(data);
            
            assertNotNull(hash);
            assertEquals(32, hash.length); // SHA-256 luôn tạo ra 32 bytes
        }

        @Test
        @DisplayName("SHA-256 hash với null input")
        void testSha256BytesNull() throws NoSuchAlgorithmException {
            byte[] hash = EncryptHelper.sha256(null);
            assertNotNull(hash);
            assertEquals(0, hash.length);
        }

        @Test
        @DisplayName("SHA-256 hash với empty array")
        void testSha256BytesEmpty() throws NoSuchAlgorithmException {
            byte[] hash = EncryptHelper.sha256(new byte[0]);
            assertNotNull(hash);
            assertEquals(0, hash.length);
        }

        @Test
        @DisplayName("SHA-256 hex cho string")
        void testSha256Hex() throws NoSuchAlgorithmException {
            String hash = EncryptHelper.sha256Hex(SAMPLE_TEXT);
            
            assertNotNull(hash);
            assertEquals(64, hash.length()); // 32 bytes = 64 hex chars
            assertTrue(hash.matches("[0-9a-f]+"));
        }

        @Test
        @DisplayName("SHA-256 hex với null input")
        void testSha256HexNull() throws NoSuchAlgorithmException {
            String hash = EncryptHelper.sha256Hex(null);
            assertEquals("", hash);
        }

        @Test
        @DisplayName("SHA-256 hex với empty string")
        void testSha256HexEmpty() throws NoSuchAlgorithmException {
            String hash = EncryptHelper.sha256Hex(EMPTY_STRING);
            assertEquals("", hash);
        }

        @Test
        @DisplayName("SHA-512 hash cho byte array")
        void testSha512Bytes() throws NoSuchAlgorithmException {
            byte[] data = SAMPLE_TEXT.getBytes(StandardCharsets.UTF_8);
            byte[] hash = EncryptHelper.sha512(data);
            
            assertNotNull(hash);
            assertEquals(64, hash.length); // SHA-512 luôn tạo ra 64 bytes
        }

        @Test
        @DisplayName("SHA-512 hex cho string")
        void testSha512Hex() throws NoSuchAlgorithmException {
            String hash = EncryptHelper.sha512Hex(SAMPLE_TEXT);
            
            assertNotNull(hash);
            assertEquals(128, hash.length()); // 64 bytes = 128 hex chars
            assertTrue(hash.matches("[0-9a-f]+"));
        }

        @Test
        @DisplayName("MD5 hash cho byte array")
        void testMd5Bytes() throws NoSuchAlgorithmException {
            byte[] data = SAMPLE_TEXT.getBytes(StandardCharsets.UTF_8);
            byte[] hash = EncryptHelper.md5(data);
            
            assertNotNull(hash);
            assertEquals(16, hash.length); // MD5 luôn tạo ra 16 bytes
        }

        @Test
        @DisplayName("MD5 hex cho string")
        void testMd5Hex() throws NoSuchAlgorithmException {
            String hash = EncryptHelper.md5Hex(SAMPLE_TEXT);
            
            assertNotNull(hash);
            assertEquals(32, hash.length()); // 16 bytes = 32 hex chars
            assertTrue(hash.matches("[0-9a-f]+"));
        }

        @Test
        @DisplayName("Tính consistency của hash functions")
        void testHashConsistency() throws NoSuchAlgorithmException {
            String text = "consistent test";
            
            // Test nhiều lần để đảm bảo kết quả giống nhau
            String sha256_1 = EncryptHelper.sha256Hex(text);
            String sha256_2 = EncryptHelper.sha256Hex(text);
            assertEquals(sha256_1, sha256_2);
            
            String sha512_1 = EncryptHelper.sha512Hex(text);
            String sha512_2 = EncryptHelper.sha512Hex(text);
            assertEquals(sha512_1, sha512_2);
            
            String md5_1 = EncryptHelper.md5Hex(text);
            String md5_2 = EncryptHelper.md5Hex(text);
            assertEquals(md5_1, md5_2);
        }
    }

    @Nested
    @DisplayName("HMAC Tests")
    class HmacTests {

        private final byte[] testKey = "secret-key-123".getBytes(StandardCharsets.UTF_8);
        private final byte[] testData = SAMPLE_TEXT.getBytes(StandardCharsets.UTF_8);

        @Test
        @DisplayName("HMAC-SHA256 cho byte array")
        void testHmacSha256() throws GeneralSecurityException {
            byte[] hmac = EncryptHelper.hmacSha256(testKey, testData);
            
            assertNotNull(hmac);
            assertEquals(32, hmac.length); // HMAC-SHA256 tạo ra 32 bytes
        }

        @Test
        @DisplayName("HMAC-SHA256 hex cho string")
        void testHmacSha256Hex() throws GeneralSecurityException {
            String hmac = EncryptHelper.hmacSha256Hex(testKey, SAMPLE_TEXT);
            
            assertNotNull(hmac);
            assertEquals(64, hmac.length()); // 32 bytes = 64 hex chars
            assertTrue(hmac.matches("[0-9a-f]+"));
        }

        @Test
        @DisplayName("HMAC-SHA512 cho byte array")
        void testHmacSha512() throws GeneralSecurityException {
            byte[] hmac = EncryptHelper.hmacSha512(testKey, testData);
            
            assertNotNull(hmac);
            assertEquals(64, hmac.length); // HMAC-SHA512 tạo ra 64 bytes
        }

        @Test
        @DisplayName("HMAC-SHA512 hex cho string")
        void testHmacSha512Hex() throws GeneralSecurityException {
            String hmac = EncryptHelper.hmacSha512Hex(testKey, SAMPLE_TEXT);
            
            assertNotNull(hmac);
            assertEquals(128, hmac.length()); // 64 bytes = 128 hex chars
            assertTrue(hmac.matches("[0-9a-f]+"));
        }

        @Test
        @DisplayName("HMAC consistency với cùng key và data")
        void testHmacConsistency() throws GeneralSecurityException {
            String hmac1 = EncryptHelper.hmacSha256Hex(testKey, SAMPLE_TEXT);
            String hmac2 = EncryptHelper.hmacSha256Hex(testKey, SAMPLE_TEXT);
            assertEquals(hmac1, hmac2);
        }

        @Test
        @DisplayName("HMAC khác nhau với key khác nhau")
        void testHmacDifferentKeys() throws GeneralSecurityException {
            byte[] key1 = "key1".getBytes(StandardCharsets.UTF_8);
            byte[] key2 = "key2".getBytes(StandardCharsets.UTF_8);
            
            String hmac1 = EncryptHelper.hmacSha256Hex(key1, SAMPLE_TEXT);
            String hmac2 = EncryptHelper.hmacSha256Hex(key2, SAMPLE_TEXT);
            
            assertNotEquals(hmac1, hmac2);
        }
    }

    @Nested
    @DisplayName("AES Encryption Tests")
    class AesEncryptionTests {

        private final byte[] testKey = new byte[32]; // 256-bit key
        private final byte[] testIv = new byte[12];  // 96-bit IV for GCM
        
        void setupTestData() {
            // Tạo key và IV cố định cho test
            for (int i = 0; i < testKey.length; i++) {
                testKey[i] = (byte) (i % 256);
            }
            for (int i = 0; i < testIv.length; i++) {
                testIv[i] = (byte) (i % 256);
            }
        }

        @Test
        @DisplayName("AES encrypt/decrypt với IV cố định")
        void testAesEncryptDecryptWithFixedIv() throws GeneralSecurityException {
            setupTestData();
            byte[] plaintext = SAMPLE_TEXT.getBytes(StandardCharsets.UTF_8);
            
            byte[] encrypted = EncryptHelper.aesEncrypt(testKey, testIv, plaintext);
            byte[] decrypted = EncryptHelper.aesDecrypt(testKey, testIv, encrypted);
            
            assertNotNull(encrypted);
            assertNotNull(decrypted);
            assertArrayEquals(plaintext, decrypted);
            
            String originalText = new String(decrypted, StandardCharsets.UTF_8);
            assertEquals(SAMPLE_TEXT, originalText);
        }

        @Test
        @DisplayName("AES encrypt/decrypt Base64 với IV cố định")
        void testAesEncryptDecryptBase64WithFixedIv() throws GeneralSecurityException {
            setupTestData();
            
            String encrypted = EncryptHelper.aesEncryptToBase64(testKey, testIv, SAMPLE_TEXT);
            String decrypted = EncryptHelper.aesDecryptFromBase64(testKey, testIv, encrypted);
            
            assertNotNull(encrypted);
            assertNotNull(decrypted);
            assertEquals(SAMPLE_TEXT, decrypted);
        }

        @Test
        @DisplayName("AES encrypt/decrypt với IV ngẫu nhiên")
        void testAesEncryptDecryptWithRandomIv() throws GeneralSecurityException {
            setupTestData();
            byte[] plaintext = SAMPLE_TEXT.getBytes(StandardCharsets.UTF_8);
            
            byte[] encrypted = EncryptHelper.aesEncrypt(testKey, plaintext);
            byte[] decrypted = EncryptHelper.aesDecrypt(testKey, encrypted);
            
            assertNotNull(encrypted);
            assertNotNull(decrypted);
            assertArrayEquals(plaintext, decrypted);
            
            // Kiểm tra IV được ghép ở đầu
            assertTrue(encrypted.length > plaintext.length + 16); // IV + plaintext + auth tag
        }

        @Test
        @DisplayName("AES encrypt/decrypt Base64 với IV ngẫu nhiên")
        void testAesEncryptDecryptBase64WithRandomIv() throws GeneralSecurityException {
            setupTestData();
            
            String encrypted = EncryptHelper.aesEncryptToBase64(testKey, SAMPLE_TEXT);
            String decrypted = EncryptHelper.aesDecryptFromBase64(testKey, encrypted);
            
            assertNotNull(encrypted);
            assertNotNull(decrypted);
            assertEquals(SAMPLE_TEXT, decrypted);
        }

        @Test
        @DisplayName("AES encryption tạo ra kết quả khác nhau với IV ngẫu nhiên")
        void testAesRandomIvGeneratesDifferentResults() throws GeneralSecurityException {
            setupTestData();
            
            String encrypted1 = EncryptHelper.aesEncryptToBase64(testKey, SAMPLE_TEXT);
            String encrypted2 = EncryptHelper.aesEncryptToBase64(testKey, SAMPLE_TEXT);
            
            // Với IV ngẫu nhiên, kết quả mã hóa phải khác nhau
            assertNotEquals(encrypted1, encrypted2);
            
            // Nhưng giải mã ra cùng kết quả
            String decrypted1 = EncryptHelper.aesDecryptFromBase64(testKey, encrypted1);
            String decrypted2 = EncryptHelper.aesDecryptFromBase64(testKey, encrypted2);
            
            assertEquals(SAMPLE_TEXT, decrypted1);
            assertEquals(SAMPLE_TEXT, decrypted2);
        }

        @Test
        @DisplayName("AES decrypt với key sai sẽ throw exception")
        void testAesDecryptWithWrongKey() throws GeneralSecurityException {
            setupTestData();
            byte[] wrongKey = new byte[32];
            // wrongKey khác với testKey
            
            String encrypted = EncryptHelper.aesEncryptToBase64(testKey, SAMPLE_TEXT);
            
            assertThrows(GeneralSecurityException.class, () -> {
                EncryptHelper.aesDecryptFromBase64(wrongKey, encrypted);
            });
        }
    }

    @Nested
    @DisplayName("RSA Encryption Tests")
    class RsaEncryptionTests {

        private KeyPair keyPair;

        void setupKeyPair() throws GeneralSecurityException {
            keyPair = EncryptHelper.generateRsaKeyPair(2048);
        }

        @Test
        @DisplayName("RSA encrypt/decrypt")
        void testRsaEncryptDecrypt() throws GeneralSecurityException {
            setupKeyPair();
            byte[] plaintext = SAMPLE_TEXT.getBytes(StandardCharsets.UTF_8);
            
            byte[] encrypted = EncryptHelper.rsaEncrypt(keyPair.getPublic(), plaintext);
            byte[] decrypted = EncryptHelper.rsaDecrypt(keyPair.getPrivate(), encrypted);
            
            assertNotNull(encrypted);
            assertNotNull(decrypted);
            assertArrayEquals(plaintext, decrypted);
        }

        @Test
        @DisplayName("RSA encrypt/decrypt Base64")
        void testRsaEncryptDecryptBase64() throws GeneralSecurityException {
            setupKeyPair();
            
            String encrypted = EncryptHelper.rsaEncryptToBase64(keyPair.getPublic(), SAMPLE_TEXT);
            String decrypted = EncryptHelper.rsaDecryptFromBase64(keyPair.getPrivate(), encrypted);
            
            assertNotNull(encrypted);
            assertNotNull(decrypted);
            assertEquals(SAMPLE_TEXT, decrypted);
        }

        @Test
        @DisplayName("RSA decrypt với private key sai sẽ throw exception")
        void testRsaDecryptWithWrongPrivateKey() throws GeneralSecurityException {
            setupKeyPair();
            KeyPair wrongKeyPair = EncryptHelper.generateRsaKeyPair(2048);
            
            String encrypted = EncryptHelper.rsaEncryptToBase64(keyPair.getPublic(), SAMPLE_TEXT);
            
            assertThrows(GeneralSecurityException.class, () -> {
                EncryptHelper.rsaDecryptFromBase64(wrongKeyPair.getPrivate(), encrypted);
            });
        }
    }

    @Nested
    @DisplayName("Password Hashing Tests")
    class PasswordHashingTests {

        @Test
        @DisplayName("BCrypt password hashing")
        void testBCryptPasswordHashing() {
            String hashed = EncryptHelper.hashPasswordBCrypt(SAMPLE_PASSWORD);
            
            assertNotNull(hashed);
            assertTrue(hashed.startsWith("$2a$") || hashed.startsWith("$2b$") || hashed.startsWith("$2y$"));
            
            // Verify password
            assertTrue(EncryptHelper.verifyPasswordBCrypt(SAMPLE_PASSWORD, hashed));
            assertFalse(EncryptHelper.verifyPasswordBCrypt("wrongPassword", hashed));
        }

        @Test
        @DisplayName("BCrypt tạo ra hash khác nhau cho cùng password")
        void testBCryptGeneratesDifferentHashes() {
            String hash1 = EncryptHelper.hashPasswordBCrypt(SAMPLE_PASSWORD);
            String hash2 = EncryptHelper.hashPasswordBCrypt(SAMPLE_PASSWORD);
            
            assertNotEquals(hash1, hash2); // Do salt ngẫu nhiên
            
            // Nhưng đều verify được
            assertTrue(EncryptHelper.verifyPasswordBCrypt(SAMPLE_PASSWORD, hash1));
            assertTrue(EncryptHelper.verifyPasswordBCrypt(SAMPLE_PASSWORD, hash2));
        }

        @Test
        @DisplayName("SCrypt password hashing")
        void testSCryptPasswordHashing() {
            String hashed = EncryptHelper.hashPasswordSCrypt(SAMPLE_PASSWORD);
            
            assertNotNull(hashed);
            assertTrue(hashed.startsWith("$s0$"));
            
            // Verify password
            assertTrue(EncryptHelper.verifyPasswordSCrypt(SAMPLE_PASSWORD, hashed));
            assertFalse(EncryptHelper.verifyPasswordSCrypt("wrongPassword", hashed));
        }

        @Test
        @DisplayName("Argon2 password hashing")
        void testArgon2PasswordHashing() {
            String hashed = EncryptHelper.hashPasswordArgon2(SAMPLE_PASSWORD);
            
            assertNotNull(hashed);
            assertTrue(hashed.startsWith("$argon2"));
            
            // Verify password
            assertTrue(EncryptHelper.verifyPasswordArgon2(SAMPLE_PASSWORD, hashed));
            assertFalse(EncryptHelper.verifyPasswordArgon2("wrongPassword", hashed));
        }

        @Test
        @DisplayName("So sánh độ an toàn các thuật toán hash password")
        void testPasswordHashingAlgorithmsComparison() {
            // Test các thuật toán đều hoạt động với cùng password
            String bcryptHash = EncryptHelper.hashPasswordBCrypt(SAMPLE_PASSWORD);
            String scryptHash = EncryptHelper.hashPasswordSCrypt(SAMPLE_PASSWORD);
            String argon2Hash = EncryptHelper.hashPasswordArgon2(SAMPLE_PASSWORD);
            
            // Tất cả đều khác nhau
            assertNotEquals(bcryptHash, scryptHash);
            assertNotEquals(bcryptHash, argon2Hash);
            assertNotEquals(scryptHash, argon2Hash);
            
            // Nhưng đều verify được với password gốc
            assertTrue(EncryptHelper.verifyPasswordBCrypt(SAMPLE_PASSWORD, bcryptHash));
            assertTrue(EncryptHelper.verifyPasswordSCrypt(SAMPLE_PASSWORD, scryptHash));
            assertTrue(EncryptHelper.verifyPasswordArgon2(SAMPLE_PASSWORD, argon2Hash));
        }
    }

    @Nested
    @DisplayName("Key Utilities Tests")
    class KeyUtilitiesTests {

        @Test
        @DisplayName("Tạo RSA key pair")
        void testGenerateRsaKeyPair() throws GeneralSecurityException {
            KeyPair keyPair2048 = EncryptHelper.generateRsaKeyPair(2048);
            KeyPair keyPair4096 = EncryptHelper.generateRsaKeyPair(4096);
            
            assertNotNull(keyPair2048);
            assertNotNull(keyPair2048.getPublic());
            assertNotNull(keyPair2048.getPrivate());
            assertEquals("RSA", keyPair2048.getPublic().getAlgorithm());
            assertEquals("RSA", keyPair2048.getPrivate().getAlgorithm());
            
            assertNotNull(keyPair4096);
            assertNotNull(keyPair4096.getPublic());
            assertNotNull(keyPair4096.getPrivate());
        }

        @Test
        @DisplayName("Tạo AES key")
        void testGenerateAesKey() throws GeneralSecurityException {
            SecretKey key128 = EncryptHelper.generateAesKey(128);
            SecretKey key192 = EncryptHelper.generateAesKey(192);
            SecretKey key256 = EncryptHelper.generateAesKey(256);
            
            assertNotNull(key128);
            assertNotNull(key192);
            assertNotNull(key256);
            
            assertEquals("AES", key128.getAlgorithm());
            assertEquals("AES", key192.getAlgorithm());
            assertEquals("AES", key256.getAlgorithm());
            
            // Kiểm tra kích thước key
            assertEquals(16, key128.getEncoded().length); // 128 bit = 16 bytes
            assertEquals(24, key192.getEncoded().length); // 192 bit = 24 bytes
            assertEquals(32, key256.getEncoded().length); // 256 bit = 32 bytes
        }

        @Test
        @DisplayName("Load public key từ PEM string")
        void testLoadPublicKeyFromPemString() throws GeneralSecurityException {
            // Tạo key pair để test
            KeyPair keyPair = EncryptHelper.generateRsaKeyPair(2048);
            PublicKey originalPublicKey = keyPair.getPublic();
            
            // Chuyển public key thành PEM format
            String pemContent = Base64.getEncoder().encodeToString(originalPublicKey.getEncoded());
            String pemString = "-----BEGIN PUBLIC KEY-----\n" + pemContent + "\n-----END PUBLIC KEY-----";
            
            // Load lại từ PEM
            PublicKey loadedKey = EncryptHelper.loadPublicKeyFromPem(pemString);
            
            assertNotNull(loadedKey);
            assertEquals("RSA", loadedKey.getAlgorithm());
            assertArrayEquals(originalPublicKey.getEncoded(), loadedKey.getEncoded());
        }

        @Test
        @DisplayName("Load private key từ PEM string")
        void testLoadPrivateKeyFromPemString() throws GeneralSecurityException {
            // Tạo key pair để test
            KeyPair keyPair = EncryptHelper.generateRsaKeyPair(2048);
            PrivateKey originalPrivateKey = keyPair.getPrivate();
            
            // Chuyển private key thành PEM format
            String pemContent = Base64.getEncoder().encodeToString(originalPrivateKey.getEncoded());
            String pemString = "-----BEGIN PRIVATE KEY-----\n" + pemContent + "\n-----END PRIVATE KEY-----";
            
            // Load lại từ PEM
            PrivateKey loadedKey = EncryptHelper.loadPrivateKeyFromPem(pemString);
            
            assertNotNull(loadedKey);
            assertEquals("RSA", loadedKey.getAlgorithm());
            assertArrayEquals(originalPrivateKey.getEncoded(), loadedKey.getEncoded());
        }

        @Test
        @DisplayName("Load key với PEM format không hợp lệ sẽ throw exception")
        void testLoadKeyFromInvalidPem() {
            String invalidPem = "invalid-pem-content";
            
            assertThrows(GeneralSecurityException.class, () -> {
                EncryptHelper.loadPublicKeyFromPem(invalidPem);
            });
            
            assertThrows(GeneralSecurityException.class, () -> {
                EncryptHelper.loadPrivateKeyFromPem(invalidPem);
            });
        }

        @Test
        @DisplayName("Load public key từ certificate bytes")
        void testLoadPublicKeyFromCertBytes() throws Exception {
            // Test với certificate bytes không hợp lệ
            byte[] invalidCertBytes = "invalid-cert-data".getBytes(StandardCharsets.UTF_8);
            
            assertThrows(Exception.class, () -> {
                EncryptHelper.loadPublicKeyFromCert(invalidCertBytes);
            });
        }

        @Test
        @DisplayName("Test key generation với kích thước khác nhau")
        void testKeyGenerationDifferentSizes() throws GeneralSecurityException {
            // Test AES key sizes
            assertDoesNotThrow(() -> EncryptHelper.generateAesKey(128));
            assertDoesNotThrow(() -> EncryptHelper.generateAesKey(192));
            assertDoesNotThrow(() -> EncryptHelper.generateAesKey(256));
            
            // Test RSA key sizes  
            assertDoesNotThrow(() -> EncryptHelper.generateRsaKeyPair(1024));
            assertDoesNotThrow(() -> EncryptHelper.generateRsaKeyPair(2048));
            // 4096 có thể chậm trong test, bỏ qua hoặc đặt timeout
        }
    }

    @Nested
    @DisplayName("Integration Tests")
    class IntegrationTests {

        @Test
        @DisplayName("Kết hợp RSA và AES để mã hóa dữ liệu lớn")
        void testRsaAesHybridEncryption() throws GeneralSecurityException {
            // Tạo RSA key pair
            KeyPair rsaKeyPair = EncryptHelper.generateRsaKeyPair(2048);
            
            // Tạo AES key và mã hóa bằng RSA
            SecretKey aesKey = EncryptHelper.generateAesKey(256);
            byte[] encryptedAesKey = EncryptHelper.rsaEncrypt(rsaKeyPair.getPublic(), aesKey.getEncoded());
            
            // Mã hóa dữ liệu lớn bằng AES
            String largeData = "This is a large piece of data that would be too big for RSA encryption directly. ".repeat(10);
            String encryptedData = EncryptHelper.aesEncryptToBase64(aesKey.getEncoded(), largeData);
            
            // Giải mã AES key bằng RSA
            byte[] decryptedAesKeyBytes = EncryptHelper.rsaDecrypt(rsaKeyPair.getPrivate(), encryptedAesKey);
            
            // Giải mã dữ liệu bằng AES
            String decryptedData = EncryptHelper.aesDecryptFromBase64(decryptedAesKeyBytes, encryptedData);
            
            assertEquals(largeData, decryptedData);
        }

        @Test
        @DisplayName("Test toàn bộ flow với tất cả các thuật toán")
        void testCompleteEncryptionFlow() throws GeneralSecurityException {
            // 1. Hash dữ liệu
            String dataHash = EncryptHelper.sha256Hex(SAMPLE_TEXT);
            assertNotNull(dataHash);
            
            // 2. Tạo HMAC để verify tính toàn vẹn
            byte[] hmacKey = "hmac-secret-key".getBytes(StandardCharsets.UTF_8);
            String hmac = EncryptHelper.hmacSha256Hex(hmacKey, SAMPLE_TEXT);
            assertNotNull(hmac);
            
            // 3. Mã hóa dữ liệu bằng AES
            SecretKey aesKey = EncryptHelper.generateAesKey(256);
            String encryptedData = EncryptHelper.aesEncryptToBase64(aesKey.getEncoded(), SAMPLE_TEXT);
            assertNotNull(encryptedData);
            
            // 4. Hash password
            String passwordHash = EncryptHelper.hashPasswordArgon2(SAMPLE_PASSWORD);
            assertNotNull(passwordHash);
            
            // 5. Verify tất cả
            String decryptedData = EncryptHelper.aesDecryptFromBase64(aesKey.getEncoded(), encryptedData);
            assertEquals(SAMPLE_TEXT, decryptedData);
            
            String verifyHash = EncryptHelper.sha256Hex(decryptedData);
            assertEquals(dataHash, verifyHash);
            
            String verifyHmac = EncryptHelper.hmacSha256Hex(hmacKey, decryptedData);
            assertEquals(hmac, verifyHmac);
            
            assertTrue(EncryptHelper.verifyPasswordArgon2(SAMPLE_PASSWORD, passwordHash));
        }
    }

    @Nested
    @DisplayName("Edge Cases and Error Handling Tests")
    class EdgeCasesTests {

        @Test
        @DisplayName("Test với null input cho các hash functions")
        void testHashFunctionsWithNull() throws NoSuchAlgorithmException {
            assertDoesNotThrow(() -> {
                EncryptHelper.sha256(null);
                EncryptHelper.sha512(null);
                EncryptHelper.md5(null);
                EncryptHelper.sha256Hex(null);
                EncryptHelper.sha512Hex(null);
                EncryptHelper.md5Hex(null);
            });
        }

        @Test
        @DisplayName("Test với empty input cho các hash functions")
        void testHashFunctionsWithEmpty() throws NoSuchAlgorithmException {
            assertDoesNotThrow(() -> {
                EncryptHelper.sha256(new byte[0]);
                EncryptHelper.sha512(new byte[0]);
                EncryptHelper.md5(new byte[0]);
                EncryptHelper.sha256Hex("");
                EncryptHelper.sha512Hex("");
                EncryptHelper.md5Hex("");
            });
        }

        @Test
        @DisplayName("Test AES với key size không hợp lệ")
        void testAesWithInvalidKeySize() {
            byte[] invalidKey = new byte[15]; // Không phải 16, 24, 32 bytes
            byte[] plaintext = SAMPLE_TEXT.getBytes(StandardCharsets.UTF_8);
            byte[] iv = new byte[12];
            
            assertThrows(GeneralSecurityException.class, () -> {
                EncryptHelper.aesEncrypt(invalidKey, iv, plaintext);
            });
        }

        @Test
        @DisplayName("Test AES với IV size không hợp lệ")
        void testAesWithInvalidIvSize() {
            byte[] key = new byte[32]; // Valid 256-bit key
            byte[] plaintext = SAMPLE_TEXT.getBytes(StandardCharsets.UTF_8);
            byte[] invalidIv = new byte[16]; // Không phải 12 bytes cho GCM
            
            assertThrows(GeneralSecurityException.class, () -> {
                EncryptHelper.aesEncrypt(key, invalidIv, plaintext);
            });
        }

        @Test
        @DisplayName("Test HMAC với empty key")
        void testHmacWithEmptyKey() throws GeneralSecurityException {
            byte[] emptyKey = new byte[0];
            byte[] data = SAMPLE_TEXT.getBytes(StandardCharsets.UTF_8);
            
            // HMAC vẫn có thể hoạt động với empty key nhưng không an toàn
            assertDoesNotThrow(() -> {
                EncryptHelper.hmacSha256(emptyKey, data);
                EncryptHelper.hmacSha512(emptyKey, data);
            });
        }

        @Test
        @DisplayName("Test password hashing với empty password")
        void testPasswordHashingWithEmptyPassword() {
            String emptyPassword = "";
            
            assertDoesNotThrow(() -> {
                String bcryptHash = EncryptHelper.hashPasswordBCrypt(emptyPassword);
                String scryptHash = EncryptHelper.hashPasswordSCrypt(emptyPassword);
                String argon2Hash = EncryptHelper.hashPasswordArgon2(emptyPassword);
                
                assertTrue(EncryptHelper.verifyPasswordBCrypt(emptyPassword, bcryptHash));
                assertTrue(EncryptHelper.verifyPasswordSCrypt(emptyPassword, scryptHash));
                assertTrue(EncryptHelper.verifyPasswordArgon2(emptyPassword, argon2Hash));
            });
        }

        @Test
        @DisplayName("Test Base64 decoding với invalid input")
        void testAesDecryptWithInvalidBase64() {
            byte[] key = new byte[32];
            String invalidBase64 = "invalid-base64-string!@#$";
            
            assertThrows(Exception.class, () -> {
                EncryptHelper.aesDecryptFromBase64(key, invalidBase64);
            });
        }

        @Test
        @DisplayName("Test RSA với dữ liệu quá lớn")
        void testRsaWithOversizedData() throws GeneralSecurityException {
            KeyPair keyPair = EncryptHelper.generateRsaKeyPair(2048);
            
            // Tạo dữ liệu lớn hơn giới hạn RSA (2048-bit key có thể encrypt tối đa ~245 bytes với OAEP)
            byte[] oversizedData = new byte[300];
            
            assertThrows(GeneralSecurityException.class, () -> {
                EncryptHelper.rsaEncrypt(keyPair.getPublic(), oversizedData);
            });
        }

        @Test
        @DisplayName("Test với Unicode và special characters")
        void testWithUnicodeAndSpecialCharacters() throws Exception {
            String unicodeText = "Tiếng Việt có dấu: áàảãạ, ÁÀẢÃẠ, 中文, 한국어, العربية, 🚀🔐💻";
            byte[] key = new byte[32];
            
            // Test hashing
            assertDoesNotThrow(() -> {
                EncryptHelper.sha256Hex(unicodeText);
                EncryptHelper.sha512Hex(unicodeText);
                EncryptHelper.md5Hex(unicodeText);
            });
            
            // Test AES encryption
            assertDoesNotThrow(() -> {
                String encrypted = EncryptHelper.aesEncryptToBase64(key, unicodeText);
                String decrypted = EncryptHelper.aesDecryptFromBase64(key, encrypted);
                assertEquals(unicodeText, decrypted);
            });
            
            // Test password hashing
            assertDoesNotThrow(() -> {
                String bcryptHash = EncryptHelper.hashPasswordBCrypt(unicodeText);
                assertTrue(EncryptHelper.verifyPasswordBCrypt(unicodeText, bcryptHash));
            });
        }
    }
}