package vn.osp.common.domain.helpers;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import java.beans.PropertyDescriptor;
import java.util.HashSet;
import java.util.Set;

public final class BeanHelper {
    
    /**
     * Private constructor để ngăn không cho khởi tạo instance của utility class
     */
    private BeanHelper() {}

    /**
     * Copy tất cả property từ source object sang target object
     * 
     * @param source object nguồn chứa dữ liệu cần copy
     * @param target object đích sẽ nhận dữ liệu được copy
     */
    public static void copy(Object source, Object target) {
        BeanUtils.copyProperties(source, target);
    }

    /**
     * Copy các property từ source sang target, bỏ qua các field có giá trị null
     * 
     * @param source object nguồn chứa dữ liệu cần copy
     * @param target object đích sẽ nhận dữ liệu được copy (chỉ copy các field không null)
     */
    public static void copyNonNull(Object source, Object target) {
        BeanUtils.copyProperties(source, target, getNullPropertyNames(source));
    }

    /**
     * Lấy danh sách tên các property có giá trị null từ source object
     * 
     * @param source object cần kiểm tra các property null
     * @return mảng string chứa tên các property có giá trị null
     */
    private static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();
        for (PropertyDescriptor pd : pds) {
            Object value = src.getPropertyValue(pd.getName());
            if (value == null) {
                emptyNames.add(pd.getName());
            }
        }
        return emptyNames.toArray(new String[0]);
    }
}

