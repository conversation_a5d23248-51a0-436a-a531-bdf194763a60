package vn.osp.common.domain.domainentitytypes;

import java.util.UUID;

/**
 * Class base, tất cả các model đều cần phải kế thừa từ class này
 * để tận dụng lại các field có sẵn, trong đó fix sẵn các key khóa ngoại là UUID.
 */
public class BaseDomainEntityUuid extends BaseDomainEntity<UUID> {

    /**
     * Khởi tạo một đối tượng BaseDomainEntity mới với Id là UUID(0...0).
     */
    public BaseDomainEntityUuid() {
        super(new UUID(0L, 0L)); // tương đương Guid.Empty trong .NET
    }
}
