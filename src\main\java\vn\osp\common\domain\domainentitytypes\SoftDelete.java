package vn.osp.common.domain.domainentitytypes;

import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.time.Instant;

/**
 * Model có thời gian xóa và trạng thái đã xóa.
 *
 * @param <TKey> <PERSON><PERSON><PERSON> dữ liệu của Id (UUID, Long, Integer, ...)
 */
public interface SoftDelete<TKey extends Serializable> {

    /**
     * Thời gian xóa.
     */
    @Nullable
    Instant getDeleted();

    void setDeleted(@Nullable Instant deleted);

    /**
     * Trạng thái đã xóa hay chưa.
     */
    boolean getIsDeleted();

    void setIsDeleted(boolean deleted);

    /**
     * Id người xóa.
     */
    @Nullable
    TKey getDeletedBy();

    void setDeletedBy(@Nullable TKey deletedBy);
}