package vn.osp.common.domain.helpers;

import java.util.UUID;

public final class UuidHelper {
    public static final UUID EMPTY = new UUID(0L, 0L);

    private UuidHelper() {}

    /**
     * Kiểm tra UUID có rỗng hoặc null không
     *
     * @param id UUID cần kiểm tra
     * @return true nếu UUID null hoặc bằng với UUID rỗng (0-0-0-0-0); false nếu ngược lại
     */
    public static boolean isEmpty(UUID id) {
        return id == null || id.equals(UuidHelper.EMPTY);
    }

    /**
     * Tạo UUID ngẫu nhiên mới
     *
     * @return UUID ngẫu nhiên được tạo bằng UUID.randomUUID()
     */
    public static UUID random() {
        return UUID.randomUUID();
    }

    /**
     * Chuyển đổi chuỗi thành UUID một cách an toàn
     * <p>
     * Method này sẽ không ném exception khi chuỗi đầu vào không hợp lệ,
     * thay vào đó sẽ trả về UUID rỗng
     *
     * @param value Chuỗi cần chuyển đổi thành UUID
     * @return UUID được chuyển đổi từ chuỗi, hoặc UUID rỗng nếu value null hoặc không hợp lệ
     */
    public static UUID fromString(String value) {
        try {
            return value != null ? UUID.fromString(value) : UuidHelper.EMPTY;
        } catch (IllegalArgumentException e) {
            return UuidHelper.EMPTY;
        }
    }
}
