package vn.osp.common.domain.domainentitytypes;

/**
 * Model có chứa property sử dụng / không sử dụng (enabled).
 * Khi truy vấn mặc định sẽ filter Enabled = true.
 * Nếu muốn lấy tất cả thì cần bỏ filter (ví dụ gửi metadata IgnoreEnabledFilter).
 */
public interface HasEnabled {

    /**
     * Trạng thái bật (sử dụng) hay không.
     */
    boolean isEnabled();

    void setEnabled(boolean enabled);
}
