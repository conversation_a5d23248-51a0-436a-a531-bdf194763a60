package vn.osp.common.domain.helpers;

import jakarta.annotation.Nullable;

import java.time.*;
import java.time.format.DateTimeFormatter;

public final class DateTimeHelper {
    
    /**
     * Private constructor để ngăn không cho khởi tạo instance của utility class
     */
    private DateTimeHelper() {
    }

    private static final DateTimeFormatter ISO_FORMATTER = DateTimeFormatter.ISO_DATE_TIME;

    /**
     * Format LocalDateTime thành chuỗi theo định dạng ISO
     * 
     * @param dateTime đối tượng LocalDateTime cần format
     * @return chuỗi định dạng ISO hoặc null nếu dateTime null
     */
    public static String formatIso(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(ISO_FORMATTER) : null;
    }

    /**
     * Parse chuỗi ISO thành LocalDateTime
     * 
     * @param value chuỗi ISO cần parse
     * @return đối tượng LocalDateTime hoặc null nếu value null
     */
    public static LocalDateTime parseIso(String value) {
        return value != null ? LocalDateTime.parse(value, ISO_FORMATTER) : null;
    }

    /**
     * Lấy thời gian hiện tại theo UTC
     * 
     * @return LocalDateTime của thời điểm hiện tại theo UTC
     */
    public static LocalDateTime nowUtc() {
        return LocalDateTime.now(ZoneOffset.UTC);
    }

    /**
     * Chuyển đổi LocalDateTime sang Instant sử dụng time zone hệ thống
     * 
     * @param dateTime đối tượng LocalDateTime cần chuyển đổi
     * @return Instant tương ứng hoặc null nếu dateTime null
     */
    public static Instant toInstant(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.atZone(ZoneId.systemDefault()).toInstant() : null;
    }

    /**
     * Chuyển đổi LocalDate sang Instant tại thời điểm bắt đầu ngày sử dụng time zone hệ thống
     * 
     * @param date đối tượng LocalDate cần chuyển đổi
     * @return Instant tương ứng hoặc null nếu date null
     */
    public static Instant toInstant(LocalDate date) {
        return date != null ? date.atStartOfDay(ZoneId.systemDefault()).toInstant() : null;
    }

    /**
     * Chuyển đổi ZonedDateTime sang Instant
     * 
     * @param dateTime đối tượng ZonedDateTime cần chuyển đổi
     * @return Instant tương ứng hoặc null nếu dateTime null
     */
    public static Instant toInstant(ZonedDateTime dateTime) {
        return dateTime != null ? dateTime.toInstant() : null;
    }

    /**
     * Chuyển đổi OffsetDateTime sang Instant
     * 
     * @param dateTime đối tượng OffsetDateTime cần chuyển đổi
     * @return Instant tương ứng hoặc null nếu dateTime null
     */
    public static Instant toInstant(OffsetDateTime dateTime) {
        return dateTime != null ? dateTime.toInstant() : null;
    }

    /**
     * Chuyển đổi chuỗi datetime sang Instant
     * Hỗ trợ format ISO Instant và ISO LocalDateTime
     * 
     * @param dateTime chuỗi datetime cần chuyển đổi
     * @return Instant tương ứng hoặc null nếu không parse được
     */
    public static Instant toInstant(String dateTime) {
        if (dateTime == null || StringHelper.isEmpty(dateTime)) {
            return null;
        }

        try {
            // Thử parse theo ISO format trước
            return Instant.parse(dateTime);
        } catch (Exception e) {
            try {
                // Thử parse như LocalDateTime rồi chuyển sang Instant
                LocalDateTime localDateTime = LocalDateTime.parse(dateTime, ISO_FORMATTER);
                return localDateTime.atZone(ZoneId.systemDefault()).toInstant();
            } catch (Exception ex) {
                return null;
            }
        }
    }

    /**
     * Format đối tượng LocalDate thành chuỗi theo pattern chỉ định
     *
     * @param date    đối tượng LocalDate cần format
     * @param pattern pattern định dạng (ví dụ: "yyyy-MM-dd HH:mm:ss")
     * @return chuỗi đã định dạng, hoặc null nếu date/pattern null hoặc lỗi
     */
    public static String format(LocalDate date, String pattern) {
        if (date == null || pattern == null || StringHelper.isEmpty(pattern)) {
            return null;
        }

        return format(toInstant(date), pattern);
    }

    /**
     * Format đối tượng Instant thành chuỗi theo pattern chỉ định
     *
     * @param date    đối tượng Instant cần format
     * @param pattern pattern định dạng (ví dụ: "yyyy-MM-dd HH:mm:ss")
     * @return chuỗi đã định dạng, hoặc null nếu date/pattern null hoặc lỗi
     */
    public static String format(Instant date, String pattern) {
        if (date == null || pattern == null || StringHelper.isEmpty(pattern)) {
            return null;
        }
        try {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern)
                    .withZone(ZoneId.systemDefault());
            return dateTimeFormatter.format(date);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    /**
     * Kiểm tra tính hợp lệ của khoảng thời gian:
     * - fromDate phải sau hoặc bằng thời điểm hiện tại
     * - toDate (nếu có) phải sau hoặc bằng fromDate
     *
     * @param fromDate ngày bắt đầu
     * @param toDate   ngày kết thúc (có thể null)
     * @return true nếu hợp lệ, false nếu không hợp lệ
     */
    public static boolean isDateRangeValid(Instant fromDate, @Nullable Instant toDate) {
        if (fromDate == null) return false;
        if (fromDate.isBefore(Instant.now())) return false;
        return toDate == null || !toDate.isBefore(fromDate);
    }

    /**
     * Kiểm tra tính hợp lệ của khoảng thời gian:
     * - fromDate phải sau hoặc bằng thời điểm hiện tại
     * - toDate (nếu có) phải sau hoặc bằng fromDate
     *
     * @param fromDate ngày bắt đầu
     * @param toDate   ngày kết thúc (có thể null)
     * @return true nếu hợp lệ, false nếu không hợp lệ
     */
    public static boolean isDateRangeValid(LocalDate fromDate, @Nullable LocalDate toDate) {
        if (fromDate == null) return false;
        if (toInstant(fromDate).isBefore(Instant.now())) return false;
        return toDate == null || !toInstant(toDate).isBefore(toInstant(fromDate));
    }

    /**
     * Kiểm tra tính hợp lệ của khoảng thời gian với ngày so sánh tùy chỉnh:
     * - fromDate phải sau hoặc bằng cpDate
     * - toDate (nếu có) phải sau fromDate
     *
     * @param fromDate ngày bắt đầu
     * @param toDate   ngày kết thúc (có thể null)
     * @param cpDate   ngày dùng để so sánh với fromDate
     * @return true nếu hợp lệ, false nếu không hợp lệ
     */
    public static boolean isDateRangeValid(LocalDate fromDate, @Nullable LocalDate toDate, LocalDate cpDate) {
        if (fromDate == null) return false;
        if (toInstant(fromDate).isBefore(toInstant(cpDate))) return false;
        return toDate == null || toInstant(toDate).isAfter(toInstant(fromDate));
    }

    /**
     * Kiểm tra tính hợp lệ của khoảng thời gian với thời điểm so sánh tùy chỉnh:
     * - fromDate phải sau hoặc bằng cpDate
     * - toDate (nếu có) phải sau fromDate
     *
     * @param fromDate ngày bắt đầu
     * @param toDate   ngày kết thúc (có thể null)
     * @param cpDate   thời điểm dùng để so sánh với fromDate
     * @return true nếu hợp lệ, false nếu không hợp lệ
     */
    public static boolean isDateRangeValid(Instant fromDate, @Nullable Instant toDate, Instant cpDate) {
        if (fromDate == null) return false;
        if (fromDate.isBefore(cpDate)) return false;
        return toDate == null || toDate.isAfter(fromDate);
    }

    /**
     * Chuyển đổi Instant sang LocalDateTime theo time zone Việt Nam
     * 
     * @param instant đối tượng Instant cần chuyển đổi
     * @return LocalDateTime theo time zone Việt Nam
     */
    public static LocalDateTime toVietnamLocalDateTime(Instant instant) {
        return LocalDateTime.ofInstant(instant, ZoneId.of("Asia/Ho_Chi_Minh"));
    }

    /**
     * Format Instant thành chuỗi định dạng tiếng Việt
     * Định dạng: "X giờ Y phút Z giây [Thứ/Chủ nhật] ngày A tháng B năm C"
     * 
     * @param instant đối tượng Instant cần format
     * @return chuỗi định dạng tiếng Việt hoặc empty string nếu instant null
     */
    public static String formatVietnameseDateTime(Instant instant) {
        if (instant == null) {
            return StringHelper.EMPTY;
        }

        LocalDateTime localDt = toVietnamLocalDateTime(instant);
        int hour = localDt.getHour();
        int minute = localDt.getMinute();
        int second = localDt.getSecond();

        DayOfWeek dayOfWeek = localDt.getDayOfWeek();
        int dayOfTheMonth = localDt.getDayOfMonth();
        int month = localDt.getMonthValue();
        int year = localDt.getYear();

        return hour + " giờ "
                + minute + " phút "
                + second + " giây "
                + getVietnameseDayName(dayOfWeek)
                + " ngày " + dayOfTheMonth
                + " tháng " + month
                + " năm " + year;
    }

    /**
     * Lấy tên ngày trong tuần bằng tiếng Việt
     * 
     * @param dayOfWeek đối tượng DayOfWeek cần chuyển đổi
     * @return tên ngày bằng tiếng Việt hoặc empty string nếu dayOfWeek null
     */
    public static String getVietnameseDayName(DayOfWeek dayOfWeek) {
        if (dayOfWeek == null) {
            return StringHelper.EMPTY;
        }

        return switch (dayOfWeek) {
            case MONDAY -> "Thứ 2";
            case TUESDAY -> "Thứ 3";
            case WEDNESDAY -> "Thứ 4";
            case THURSDAY -> "Thứ 5";
            case FRIDAY -> "Thứ 6";
            case SATURDAY -> "Thứ 7";
            case SUNDAY -> "Chủ nhật";
        };
    }
}